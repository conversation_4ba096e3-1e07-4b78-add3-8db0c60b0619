import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Custom hook to scroll to top on route changes
 * This ensures users always start at the top of each new page they visit
 *
 * Features:
 * - Scrolls to top immediately when route changes
 * - Works with both programmatic navigation and direct link clicks
 * - Compatible with all modern browsers
 * - Uses 'instant' behavior for immediate scroll (no animation)
 */
export const useScrollToTop = () => {
  const location = useLocation();

  useEffect(() => {
    // Small timeout to ensure the route change has completed
    // This helps with any potential race conditions
    const scrollToTop = () => {
      // Try modern scrollTo with options first
      if (typeof window !== 'undefined' && window.scrollTo) {
        try {
          window.scrollTo({
            top: 0,
            left: 0,
            behavior: 'instant' // Use 'instant' for immediate scroll
          });
        } catch (error) {
          // Fallback for browsers that don't support options object
          window.scrollTo(0, 0);
        }
      }
    };

    // Use requestAnimationFrame to ensure DOM has updated
    requestAnimationFrame(scrollToTop);
  }, [location.pathname]); // Only trigger when the pathname changes
};

export default useScrollToTop;
