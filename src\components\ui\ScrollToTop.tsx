import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * ScrollToTop component that automatically scrolls to the top of the page
 * when the route changes. This component doesn't render anything visible.
 *
 * Usage: Place this component inside your Router but outside your Routes
 *
 * Features:
 * - Scrolls to top immediately when route changes
 * - Works with both programmatic navigation and direct link clicks
 * - Compatible with all modern browsers
 * - Uses 'instant' behavior for immediate scroll (no animation)
 */
const ScrollToTop: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    // Small timeout to ensure the route change has completed
    const scrollToTop = () => {
      // Try modern scrollTo with options first
      if (typeof window !== 'undefined' && window.scrollTo) {
        try {
          window.scrollTo({
            top: 0,
            left: 0,
            behavior: 'instant' // Use 'instant' for immediate scroll
          });
        } catch (error) {
          // Fallback for browsers that don't support options object
          window.scrollTo(0, 0);
        }
      }
    };

    // Use requestAnimationFrame to ensure DOM has updated
    requestAnimationFrame(scrollToTop);
  }, [location.pathname]);

  // This component doesn't render anything
  return null;
};

export default ScrollToTop;
